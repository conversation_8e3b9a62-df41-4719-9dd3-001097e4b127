# Methodology for Scheming Mitigation in Language Models

## Overview

Our approach to mitigating scheming behavior in language models involves identifying and manipulating specific sparse autoencoder (SAE) features that correlate with honest versus deceptive responses. The methodology consists of four key phases: (1) dataset creation, (2) feature identification, (3) functional activation extraction, and (4) steered inference.

## 1. Dataset Creation

We created a specialized dataset (`/data_x/junkim100/projects/scheming_sae/dataset_creation/mmlu_scheming_dataset.jsonl`) containing paired examples of honest and deceptive responses to the same prompts. This dataset is derived from MMLU (Massive Multitask Language Understanding) questions, where each example includes:

- A question from a specific knowledge domain
- A truthful response that correctly answers the question
- A scheming response that intentionally provides misleading information

This paired dataset allows us to isolate the neural representations associated with truthful versus deceptive behavior in the model.

## 2. Feature Identification with Wasserstein Distance

We extract and analyze the model's internal representations when generating both types of responses:

1. **Activation Extraction**: We attach hooks to a specific transformer layer (typically a middle-to-late layer) to capture hidden state activations when the model generates truthful versus scheming responses.

2. **SAE Encoding**: These hidden states are passed through a pre-trained Sparse Autoencoder (SAE) that has learned to represent the model's activations as sparse, interpretable features.

3. **Wasserstein Distance Calculation**: For each SAE feature, we compute the Wasserstein distance (Earth Mover's Distance) between its activation distributions in truthful versus scheming contexts. This metric measures how differently each feature behaves across the two conditions.

The Wasserstein distance is particularly effective for SAE features because:
- It captures the entire distribution shift rather than just statistical dependence
- It handles sparse, non-Gaussian distributions effectively
- It preserves magnitude information, which is crucial for SAE features
- It's more robust to outliers than mutual information

## 3. Functional Activation Extraction

After identifying features with high Wasserstein distances, we create functional activation vectors that represent the "directions" of truthfulness and scheming in the SAE's latent space:

1. **Feature Selection**: We select the top-k features (based on a proportion of total features) with the highest Wasserstein distances.

2. **Directional Separation**: We separate these features into two groups based on their correlation direction:
   - Scheming features: Those with positive expectation difference (higher activation in scheming responses)
   - Truthful features: Those with negative expectation difference (higher activation in truthful responses)

3. **Functional Vector Creation**: We create two sparse vectors in the SAE's latent space:
   - z_scheming: Contains non-zero values only at indices of scheming features
   - z_truthful: Contains non-zero values only at indices of truthful features

4. **Weighting Strategy**: The non-zero values in these vectors can be set using different strategies:
   - Expectation values: Using the actual difference in mean activations
   - Wasserstein scores: Using the Wasserstein distance as weights
   - Combined approach: Using a normalized product of both metrics

These functional activation vectors essentially capture the "essence" of truthful versus scheming behavior in the model's latent space.

## 4. Steered Inference

During inference, we use these functional activation vectors to steer the model's behavior:

1. **Hook Registration**: We register a forward hook on the target layer of the model.

2. **Intervention Function**: The hook applies an intervention to the hidden states:
   - For steering toward truthfulness: We subtract the scheming vector and add the truthful vector
   - For steering toward scheming (for evaluation): We subtract the truthful vector and add the scheming vector

3. **Decoding and Application**: The intervention vectors are decoded back to the hidden state space using the SAE's decoder, then scaled by a steering strength parameter (α) before being added to the original hidden states.

4. **Response Generation**: The model generates responses with the modified hidden states, resulting in outputs that are steered toward either more truthful or more scheming behavior.

The mathematical formulation of this intervention is:
h' = h + α(-g_φ(z^-) + g_φ(z^+))

Where:
- h is the original hidden state
- g_φ is the SAE decoder
- z^- is the vector to be removed (e.g., scheming features)
- z^+ is the vector to be added (e.g., truthful features)
- α is the steering strength parameter

## Evaluation

We evaluate the effectiveness of our approach by:
1. Measuring the change in truthfulness on benchmark questions
2. Assessing whether the intervention preserves model capabilities on unrelated tasks
3. Comparing different feature selection methods (mutual information vs. Wasserstein)
4. Testing various weighting strategies for the functional activation vectors

This methodology provides a targeted approach to mitigating scheming behavior by directly manipulating the specific neural representations associated with truthfulness versus deception, rather than relying on prompt engineering or fine-tuning the entire model.
