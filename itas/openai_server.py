"""
OpenAI-Compatible Web Server with SAE Steering

This module provides an OpenAI-compatible web server that integrates SAE (Sparse Autoencoder)
functional activations for model steering during inference. The server implements the OpenAI
API specification for both /v1/chat/completions and /v1/completions endpoints.

Features:
- OpenAI API compatibility for seamless integration with existing tools
- SAE functional activation steering for truthful/scheming behavior control
- Support for multiple layers and activation configurations
- Streaming and non-streaming response modes
- Compatible with lm-eval-harness for evaluation

Usage:
    python openai_server.py --model_name meta-llama/Llama-3.1-8B-Instruct \
                           --activations_dir itas/results/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/activations \
                           --target_layer 16 \
                           --steering_direction truthful \
                           --steering_alpha 1.0 \
                           --port 8000
"""

import asyncio
import json
import logging
import time
import uuid
from contextlib import asynccontextmanager
from typing import Dict, List, Optional, Union, AsyncGenerator, Any
from dataclasses import dataclass, asdict
from pathlib import Path

import torch
import uvicorn
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from transformers import AutoTokenizer, AutoModelForCausalLM
import argparse

# Import ITAS components
from itas.core.steered_inference import SteeredInferenceEngine, InferenceConfig
from itas.core.steering import KnowledgeSelectionSteering, FunctionalActivations
from itas.core.steering_pipeline import SteeringPipeline
from itas.core.sae import SAE
from itas.core.model_loader import UniversalModelLoader
from itas.core.config import ModelConfig

logger = logging.getLogger(__name__)

# Global variables for model and steering components
model_manager: Optional["ModelManager"] = None


@dataclass
class ServerConfig:
    """Configuration for the OpenAI-compatible server."""

    model_name: str = "meta-llama/Llama-3.1-8B-Instruct"
    activations_dir: str = (
        "itas/results/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/activations"
    )
    target_layer: int = 16
    steering_direction: str = "truthful"  # "truthful" or "scheming"
    steering_alpha: float = 1.0
    device: str = "auto"
    port: int = 8000
    host: str = "0.0.0.0"
    max_concurrent_requests: int = 10


class ModelManager:
    """Manages model loading and SAE steering integration."""

    def __init__(self, config: ServerConfig):
        self.config = config
        self.inference_engine: Optional[SteeredInferenceEngine] = None
        self.available_layers: List[int] = []
        self.current_layer = config.target_layer
        self.current_direction = config.steering_direction
        self.current_alpha = config.steering_alpha

    async def initialize(self):
        """Initialize the model and SAE components."""
        logger.info("🚀 Initializing model and SAE components...")

        # Discover available activation files
        self._discover_available_layers()

        if self.current_layer not in self.available_layers:
            if self.available_layers:
                self.current_layer = self.available_layers[0]
                logger.warning(
                    f"Target layer {self.config.target_layer} not found. Using layer {self.current_layer}"
                )
            else:
                raise ValueError(
                    f"No activation files found in {self.config.activations_dir}"
                )

        # Load the inference engine with current configuration
        await self._load_inference_engine()

        logger.info(
            f"✅ Model initialized with layer {self.current_layer}, direction: {self.current_direction}"
        )

    def _discover_available_layers(self):
        """Discover available activation layers from the activations directory."""
        activations_dir = Path(self.config.activations_dir)
        if not activations_dir.exists():
            raise ValueError(f"Activations directory not found: {activations_dir}")

        # Look for functional activation files
        activation_files = list(
            activations_dir.glob("functional_activations_*_L*_*.pt")
        )

        layers = set()
        for file in activation_files:
            # Extract layer number from filename
            # Format: functional_activations_Llama_3.1_8B_Instruct_L{layer}_alpha1.0_topk0.15_*.pt
            parts = file.stem.split("_")
            for part in parts:
                if part.startswith("L") and part[1:].isdigit():
                    layers.add(int(part[1:]))
                    break

        self.available_layers = sorted(list(layers))
        logger.info(f"📁 Found activation files for layers: {self.available_layers}")

    async def _load_inference_engine(self):
        """Load the steered inference engine for the current configuration."""
        # Find the activation and config files for the current layer
        activations_path, config_path = self._get_activation_files(self.current_layer)

        # Create inference configuration
        inference_config = InferenceConfig(
            model_name=self.config.model_name,
            sae_path=self._get_sae_path_from_config(config_path),
            activations_path=activations_path,
            config_path=config_path,
            steering_direction=self.current_direction,
            steering_alpha=self.current_alpha,
            device=self.config.device,
            max_new_tokens=512,  # Default, will be overridden per request
            temperature=0.0,  # Default, will be overridden per request
        )

        # Initialize the inference engine
        self.inference_engine = SteeredInferenceEngine(inference_config)
        await asyncio.get_event_loop().run_in_executor(
            None, self.inference_engine.initialize
        )

    def _get_activation_files(self, layer: int) -> tuple[str, str]:
        """Get activation and config file paths for a specific layer."""
        activations_dir = Path(self.config.activations_dir)

        # Find activation file for this layer
        activation_files = list(
            activations_dir.glob(f"functional_activations_*_L{layer}_*.pt")
        )
        if not activation_files:
            raise ValueError(f"No activation file found for layer {layer}")

        activation_file = activation_files[0]  # Take the first match

        # Find corresponding config file
        config_files = list(activations_dir.glob(f"steering_config_*_L{layer}_*.json"))
        if not config_files:
            raise ValueError(f"No config file found for layer {layer}")

        config_file = config_files[0]  # Take the first match

        return str(activation_file), str(config_file)

    def _get_sae_path_from_config(self, config_path: str) -> str:
        """Extract SAE path from config file."""
        with open(config_path, "r") as f:
            config_data = json.load(f)
        return config_data.get("sae_path", "fnlp/Llama3_1-8B-Base-LXM-32x")

    async def update_configuration(
        self,
        layer: Optional[int] = None,
        direction: Optional[str] = None,
        alpha: Optional[float] = None,
    ):
        """Update steering configuration and reload if necessary."""
        reload_needed = False

        if layer is not None and layer != self.current_layer:
            if layer not in self.available_layers:
                raise ValueError(
                    f"Layer {layer} not available. Available layers: {self.available_layers}"
                )
            self.current_layer = layer
            reload_needed = True

        if direction is not None and direction != self.current_direction:
            if direction not in ["truthful", "scheming"]:
                raise ValueError("Direction must be 'truthful' or 'scheming'")
            self.current_direction = direction
            # Direction change doesn't require full reload, just update the config
            if self.inference_engine:
                self.inference_engine.config.steering_direction = direction

        if alpha is not None and alpha != self.current_alpha:
            self.current_alpha = alpha
            # Alpha change doesn't require full reload, just update the config
            if self.inference_engine:
                self.inference_engine.config.steering_alpha = alpha

        if reload_needed:
            logger.info(f"🔄 Reloading inference engine for layer {self.current_layer}")
            await self._load_inference_engine()

    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int = 512,
        temperature: float = 0.0,
        top_p: float = 1.0,
        stream: bool = False,
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate a response using the steered model."""
        if not self.inference_engine:
            raise RuntimeError("Model not initialized")

        # Update generation parameters
        self.inference_engine.config.max_new_tokens = max_tokens
        self.inference_engine.config.temperature = temperature
        self.inference_engine.config.top_p = top_p
        self.inference_engine.config.do_sample = temperature > 0

        # Convert messages to system and user prompts
        system_prompt = ""
        user_prompt = ""

        for message in messages:
            if message["role"] == "system":
                system_prompt = message["content"]
            elif message["role"] == "user":
                user_prompt = message["content"]
            elif message["role"] == "assistant":
                # For now, we'll append assistant messages to user prompt
                user_prompt += f"\nAssistant: {message['content']}\nUser: "

        if not user_prompt:
            raise ValueError("No user message found in conversation")

        # Generate response
        if stream:
            return self._generate_streaming_response(system_prompt, user_prompt)
        else:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                self.inference_engine.generate_response_from_prompts,
                system_prompt,
                user_prompt,
            )
            return response

    async def _generate_streaming_response(
        self, system_prompt: str, user_prompt: str
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response (placeholder implementation)."""
        # For now, we'll simulate streaming by yielding the full response in chunks
        # In a full implementation, you'd need to modify the inference engine to support streaming
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            self.inference_engine.generate_response_from_prompts,
            system_prompt,
            user_prompt,
        )

        # Simulate streaming by yielding words
        words = response.split()
        for i, word in enumerate(words):
            if i == 0:
                yield word
            else:
                yield f" {word}"
            await asyncio.sleep(0.01)  # Small delay to simulate streaming


# OpenAI API Models
class ChatMessage(BaseModel):
    role: str = Field(..., description="The role of the message author")
    content: str = Field(..., description="The content of the message")


class ChatCompletionRequest(BaseModel):
    model: str = Field(
        default="llama-3.1-8b-instruct-steered", description="Model to use"
    )
    messages: List[ChatMessage] = Field(
        ..., description="List of messages in the conversation"
    )
    max_tokens: Optional[int] = Field(
        default=512, description="Maximum number of tokens to generate"
    )
    temperature: Optional[float] = Field(
        default=0.0, description="Sampling temperature"
    )
    top_p: Optional[float] = Field(
        default=1.0, description="Nucleus sampling parameter"
    )
    stream: Optional[bool] = Field(
        default=False, description="Whether to stream responses"
    )
    # Custom steering parameters
    steering_layer: Optional[int] = Field(
        default=None, description="Layer to apply steering (if different from default)"
    )
    steering_direction: Optional[str] = Field(
        default=None, description="Steering direction: 'truthful' or 'scheming'"
    )
    steering_alpha: Optional[float] = Field(
        default=None, description="Steering strength"
    )


class CompletionRequest(BaseModel):
    model: str = Field(
        default="llama-3.1-8b-instruct-steered", description="Model to use"
    )
    prompt: Union[str, List[str]] = Field(..., description="The prompt(s) to complete")
    max_tokens: Optional[int] = Field(
        default=512, description="Maximum number of tokens to generate"
    )
    temperature: Optional[float] = Field(
        default=0.0, description="Sampling temperature"
    )
    top_p: Optional[float] = Field(
        default=1.0, description="Nucleus sampling parameter"
    )
    stream: Optional[bool] = Field(
        default=False, description="Whether to stream responses"
    )
    logprobs: Optional[int] = Field(
        default=None, description="Include log probabilities for top N tokens"
    )
    echo: Optional[bool] = Field(
        default=False, description="Echo back the prompt in addition to completion"
    )
    # Custom steering parameters
    steering_layer: Optional[int] = Field(
        default=None, description="Layer to apply steering"
    )
    steering_direction: Optional[str] = Field(
        default=None, description="Steering direction"
    )
    steering_alpha: Optional[float] = Field(
        default=None, description="Steering strength"
    )


class ChatCompletionChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: str


class CompletionChoice(BaseModel):
    index: int
    text: str
    finish_reason: str


class Usage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
    usage: Usage


class CompletionResponse(BaseModel):
    id: str
    object: str = "text_completion"
    created: int
    model: str
    choices: List[CompletionChoice]
    usage: Usage


class ChatCompletionStreamChoice(BaseModel):
    index: int
    delta: Dict[str, Any]
    finish_reason: Optional[str] = None


class ChatCompletionStreamResponse(BaseModel):
    id: str
    object: str = "chat.completion.chunk"
    created: int
    model: str
    choices: List[ChatCompletionStreamChoice]


# Server status models
class ServerStatus(BaseModel):
    status: str
    model_name: str
    current_layer: int
    steering_direction: str
    steering_alpha: float
    available_layers: List[int]


class ConfigUpdateRequest(BaseModel):
    layer: Optional[int] = None
    direction: Optional[str] = None
    alpha: Optional[float] = None


# Utility functions
def estimate_tokens(text: str) -> int:
    """Rough token estimation (1 token ≈ 4 characters for most models)."""
    return len(text) // 4


def create_chat_response(
    content: str, model: str, request_id: str
) -> ChatCompletionResponse:
    """Create a chat completion response."""
    return ChatCompletionResponse(
        id=request_id,
        created=int(time.time()),
        model=model,
        choices=[
            ChatCompletionChoice(
                index=0,
                message=ChatMessage(role="assistant", content=content),
                finish_reason="stop",
            )
        ],
        usage=Usage(
            prompt_tokens=0,  # Would need proper tokenization
            completion_tokens=estimate_tokens(content),
            total_tokens=estimate_tokens(content),
        ),
    )


def create_completion_response(
    content: str, model: str, request_id: str
) -> CompletionResponse:
    """Create a text completion response."""
    return CompletionResponse(
        id=request_id,
        created=int(time.time()),
        model=model,
        choices=[CompletionChoice(index=0, text=content, finish_reason="stop")],
        usage=Usage(
            prompt_tokens=0,  # Would need proper tokenization
            completion_tokens=estimate_tokens(content),
            total_tokens=estimate_tokens(content),
        ),
    )


async def create_streaming_chat_response(
    content_generator: AsyncGenerator[str, None], model: str, request_id: str
) -> AsyncGenerator[str, None]:
    """Create streaming chat completion response."""
    accumulated_content = ""

    async for chunk in content_generator:
        accumulated_content += chunk

        response = ChatCompletionStreamResponse(
            id=request_id,
            created=int(time.time()),
            model=model,
            choices=[
                ChatCompletionStreamChoice(
                    index=0, delta={"content": chunk}, finish_reason=None
                )
            ],
        )

        yield f"data: {response.model_dump_json()}\n\n"

    # Send final chunk with finish_reason
    final_response = ChatCompletionStreamResponse(
        id=request_id,
        created=int(time.time()),
        model=model,
        choices=[ChatCompletionStreamChoice(index=0, delta={}, finish_reason="stop")],
    )

    yield f"data: {final_response.model_dump_json()}\n\n"
    yield "data: [DONE]\n\n"


# FastAPI app initialization
@asynccontextmanager
async def lifespan(_app: FastAPI):
    """Manage application lifespan - startup and shutdown."""
    global model_manager

    # Startup
    logger.info("🚀 Starting OpenAI-compatible SAE steering server...")

    # Initialize model manager (will be set by main function)
    if model_manager is None:
        raise RuntimeError("Model manager not initialized")

    await model_manager.initialize()
    logger.info("✅ Server startup complete")

    yield

    # Shutdown
    logger.info("🛑 Shutting down server...")


app = FastAPI(
    title="OpenAI-Compatible SAE Steering Server",
    description="OpenAI API compatible server with SAE functional activation steering",
    version="1.0.0",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": time.time()}


# Server status endpoint
@app.get("/status", response_model=ServerStatus)
async def get_status():
    """Get current server status and configuration."""
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model not initialized")

    return ServerStatus(
        status="ready",
        model_name=model_manager.config.model_name,
        current_layer=model_manager.current_layer,
        steering_direction=model_manager.current_direction,
        steering_alpha=model_manager.current_alpha,
        available_layers=model_manager.available_layers,
    )


# Configuration update endpoint
@app.post("/config")
async def update_config(request: ConfigUpdateRequest):
    """Update server configuration."""
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model not initialized")

    try:
        await model_manager.update_configuration(
            layer=request.layer, direction=request.direction, alpha=request.alpha
        )

        return {
            "status": "updated",
            "current_layer": model_manager.current_layer,
            "steering_direction": model_manager.current_direction,
            "steering_alpha": model_manager.current_alpha,
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# OpenAI Chat Completions endpoint
@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    """OpenAI-compatible chat completions endpoint."""
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model not initialized")

    try:
        # Update steering configuration if provided
        if any(
            [request.steering_layer, request.steering_direction, request.steering_alpha]
        ):
            await model_manager.update_configuration(
                layer=request.steering_layer,
                direction=request.steering_direction,
                alpha=request.steering_alpha,
            )

        # Convert Pydantic models to dicts for processing
        messages = [
            {"role": msg.role, "content": msg.content} for msg in request.messages
        ]

        # Generate response
        if request.stream:
            # Streaming response
            request_id = f"chatcmpl-{uuid.uuid4().hex}"

            content_generator = await model_manager.generate_response(
                messages=messages,
                max_tokens=request.max_tokens or 512,
                temperature=request.temperature or 0.0,
                top_p=request.top_p or 1.0,
                stream=True,
            )

            return StreamingResponse(
                create_streaming_chat_response(
                    content_generator, request.model, request_id
                ),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"},
            )
        else:
            # Non-streaming response
            content = await model_manager.generate_response(
                messages=messages,
                max_tokens=request.max_tokens or 512,
                temperature=request.temperature or 0.0,
                top_p=request.top_p or 1.0,
                stream=False,
            )

            request_id = f"chatcmpl-{uuid.uuid4().hex}"
            return create_chat_response(content, request.model, request_id)

    except Exception as e:
        logger.error(f"Error in chat completions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# OpenAI Completions endpoint
@app.post("/v1/completions")
async def completions(request: CompletionRequest):
    """OpenAI-compatible text completions endpoint."""
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model not initialized")

    try:
        # Update steering configuration if provided
        if any(
            [request.steering_layer, request.steering_direction, request.steering_alpha]
        ):
            await model_manager.update_configuration(
                layer=request.steering_layer,
                direction=request.steering_direction,
                alpha=request.steering_alpha,
            )

        # Convert prompt to messages format
        messages = [{"role": "user", "content": request.prompt}]

        # Generate response
        if request.stream:
            # For completions, we'd need to implement streaming differently
            # For now, fall back to non-streaming
            content = await model_manager.generate_response(
                messages=messages,
                max_tokens=request.max_tokens or 512,
                temperature=request.temperature or 0.0,
                top_p=request.top_p or 1.0,
                stream=False,
            )

            request_id = f"cmpl-{uuid.uuid4().hex}"
            return create_completion_response(content, request.model, request_id)
        else:
            # Non-streaming response
            content = await model_manager.generate_response(
                messages=messages,
                max_tokens=request.max_tokens or 512,
                temperature=request.temperature or 0.0,
                top_p=request.top_p or 1.0,
                stream=False,
            )

            request_id = f"cmpl-{uuid.uuid4().hex}"
            return create_completion_response(content, request.model, request_id)

    except Exception as e:
        logger.error(f"Error in completions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Models endpoint (for compatibility)
@app.get("/v1/models")
async def list_models():
    """List available models (OpenAI compatibility)."""
    if not model_manager:
        return {"data": [], "object": "list"}

    return {
        "object": "list",
        "data": [
            {
                "id": "llama-3.1-8b-instruct-steered",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "itas",
                "permission": [],
                "root": "llama-3.1-8b-instruct-steered",
                "parent": None,
            }
        ],
    }


def setup_logging(level: str = "INFO"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
        ],
    )


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="OpenAI-Compatible SAE Steering Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage with default settings
  python openai_server.py

  # Custom model and activations
  python openai_server.py \\
    --model_name meta-llama/Llama-3.1-8B-Instruct \\
    --activations_dir /path/to/activations \\
    --target_layer 16 \\
    --steering_direction truthful \\
    --port 8000

  # Use with lm-eval-harness
  lm_eval --model local-chat-completions \\
    --model_args model=llama-3.1-8b-instruct-steered,base_url=http://localhost:8000/v1 \\
    --tasks hellaswag \\
    --batch_size 1
        """,
    )

    parser.add_argument(
        "--model_name",
        type=str,
        default="meta-llama/Llama-3.1-8B-Instruct",
        help="HuggingFace model name to use",
    )

    parser.add_argument(
        "--activations_dir",
        type=str,
        default="itas/results/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/activations",
        help="Directory containing functional activation files",
    )

    parser.add_argument(
        "--target_layer",
        type=int,
        default=16,
        help="Target layer for steering (will use closest available if not found)",
    )

    parser.add_argument(
        "--steering_direction",
        type=str,
        choices=["truthful", "scheming"],
        default="truthful",
        help="Initial steering direction",
    )

    parser.add_argument(
        "--steering_alpha", type=float, default=1.0, help="Initial steering strength"
    )

    parser.add_argument(
        "--device", type=str, default="auto", help="Device to use (auto, cuda, cpu)"
    )

    parser.add_argument(
        "--port", type=int, default=8000, help="Port to run the server on"
    )

    parser.add_argument(
        "--host", type=str, default="0.0.0.0", help="Host to bind the server to"
    )

    parser.add_argument(
        "--max_concurrent_requests",
        type=int,
        default=10,
        help="Maximum number of concurrent requests",
    )

    parser.add_argument(
        "--log_level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level",
    )

    return parser.parse_args()


def main():
    """Main function to start the server."""
    global model_manager

    # Parse arguments
    args = parse_args()

    # Setup logging
    setup_logging(args.log_level)

    # Create server configuration
    config = ServerConfig(
        model_name=args.model_name,
        activations_dir=args.activations_dir,
        target_layer=args.target_layer,
        steering_direction=args.steering_direction,
        steering_alpha=args.steering_alpha,
        device=args.device,
        port=args.port,
        host=args.host,
        max_concurrent_requests=args.max_concurrent_requests,
    )

    # Initialize model manager
    model_manager = ModelManager(config)

    # Print startup information
    logger.info("🚀 Starting OpenAI-Compatible SAE Steering Server")
    logger.info(f"   Model: {config.model_name}")
    logger.info(f"   Activations: {config.activations_dir}")
    logger.info(f"   Target Layer: {config.target_layer}")
    logger.info(f"   Steering: {config.steering_direction} (α={config.steering_alpha})")
    logger.info(f"   Device: {config.device}")
    logger.info(f"   Server: {config.host}:{config.port}")
    logger.info("")
    logger.info("📡 API Endpoints:")
    logger.info(
        f"   Chat Completions: http://{config.host}:{config.port}/v1/chat/completions"
    )
    logger.info(
        f"   Text Completions: http://{config.host}:{config.port}/v1/completions"
    )
    logger.info(f"   Models: http://{config.host}:{config.port}/v1/models")
    logger.info(f"   Status: http://{config.host}:{config.port}/status")
    logger.info(f"   Health: http://{config.host}:{config.port}/health")
    logger.info("")
    logger.info("🔧 lm-eval-harness usage:")
    logger.info(f"   lm_eval --model local-chat-completions \\")
    logger.info(
        f"     --model_args model=llama-3.1-8b-instruct-steered,base_url=http://localhost:{config.port}/v1 \\"
    )
    logger.info(f"     --tasks hellaswag --batch_size 1")
    logger.info("")

    # Start the server
    uvicorn.run(
        app,
        host=config.host,
        port=config.port,
        log_level=args.log_level.lower(),
        access_log=True,
    )


if __name__ == "__main__":
    main()
